#!/usr/bin/env python3
"""Quick vocabulary check without loading full model."""

from transformers import AutoTokenizer

def main():
    print("🔍 Quick vocabulary size check...")
    
    # Check base model tokenizer
    tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2.5-7B-Instruct", trust_remote_code=True)
    print(f"Base Qwen2.5-7B-Instruct tokenizer size: {len(tokenizer)}")
    
    # Check trained model tokenizer
    try:
        trained_tokenizer = AutoTokenizer.from_pretrained("outputs/limo/original-817-lora", trust_remote_code=True)
        print(f"Trained LoRA tokenizer size: {len(trained_tokenizer)}")
        
        # Check for think tokens
        vocab = trained_tokenizer.get_vocab()
        think_start_id = vocab.get("<think>", None)
        think_end_id = vocab.get("</think>", None)
        
        print(f"<think> token ID: {think_start_id}")
        print(f"</think> token ID: {think_end_id}")
        
        if think_start_id and think_end_id:
            print(f"✅ Think tokens found at positions {think_start_id} and {think_end_id}")
            print(f"Expected vocab size: {max(think_start_id, think_end_id) + 1}")
        
    except Exception as e:
        print(f"❌ Error checking trained model: {e}")

if __name__ == "__main__":
    main()
