#!/usr/bin/env python3
"""
Check vocabulary sizes to understand the mismatch.
"""

from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

def check_base_model_vocab():
    """Check the base model vocabulary size."""
    print("🔍 Checking base model vocabulary sizes...")
    
    model_name = "Qwen/Qwen2.5-7B-Instruct"
    
    # Load tokenizer
    print(f"\n📝 Loading tokenizer: {model_name}")
    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
    print(f"   Tokenizer vocab size: {len(tokenizer)}")
    
    # Load model
    print(f"\n🤖 Loading model: {model_name}")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        device_map="cpu",  # Use CPU to avoid GPU memory issues
        trust_remote_code=True,
    )
    
    # Check model embedding size
    embedding_size = model.get_input_embeddings().weight.shape[0]
    print(f"   Model embedding size: {embedding_size}")
    print(f"   Model config vocab_size: {model.config.vocab_size}")
    
    # Check if they match
    if len(tokenizer) == embedding_size:
        print("✅ Tokenizer and model embedding sizes match")
    else:
        print(f"❌ MISMATCH: Tokenizer ({len(tokenizer)}) vs Model ({embedding_size})")
    
    # Add think tokens and see what happens
    print(f"\n🧠 Adding think tokens...")
    think_tokens = {"additional_special_tokens": ["<think>", "</think>"]}
    num_added = tokenizer.add_special_tokens(think_tokens)
    print(f"   Added {num_added} special tokens")
    print(f"   New tokenizer vocab size: {len(tokenizer)}")
    
    # This is what should happen during training
    print(f"\n🔧 Simulating training setup...")
    print(f"   Original model embedding size: {embedding_size}")
    print(f"   Target size after adding think tokens: {len(tokenizer)}")
    print(f"   Difference: {len(tokenizer) - embedding_size}")
    
    return {
        "original_tokenizer_size": len(tokenizer) - num_added,
        "original_model_size": embedding_size,
        "final_tokenizer_size": len(tokenizer),
        "tokens_added": num_added
    }

def check_trained_model_vocab():
    """Check vocabulary sizes in trained models."""
    models_to_check = [
        "outputs/limo/original-817-lora",
        "outputs/limo/code_switched-817-lora"
    ]
    
    for model_path in models_to_check:
        print(f"\n🔍 Checking trained model: {model_path}")
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            print(f"   Trained tokenizer vocab size: {len(tokenizer)}")
            
            # Check if think tokens are present
            if "<think>" in tokenizer.get_vocab() and "</think>" in tokenizer.get_vocab():
                print("   ✅ Think tokens found in trained tokenizer")
            else:
                print("   ❌ Think tokens NOT found in trained tokenizer")
                
        except Exception as e:
            print(f"   ❌ Error loading {model_path}: {e}")

def main():
    """Main function."""
    print("🚀 Vocabulary Size Analysis")
    print("=" * 60)
    
    # Check base model
    base_info = check_base_model_vocab()
    
    # Check trained models
    check_trained_model_vocab()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    print(f"Base model original vocab size: {base_info['original_model_size']}")
    print(f"Base tokenizer original size: {base_info['original_tokenizer_size']}")
    print(f"Think tokens added: {base_info['tokens_added']}")
    print(f"Expected final size: {base_info['final_tokenizer_size']}")
    
    print(f"\n💡 The merge script should expect:")
    print(f"   - Base model embedding size: {base_info['original_model_size']}")
    print(f"   - LoRA target size: {base_info['final_tokenizer_size']}")
    print(f"   - Resize needed: {base_info['original_model_size']} → {base_info['final_tokenizer_size']}")

if __name__ == "__main__":
    main()
