#!/usr/bin/env python3
"""
Merge LoRA adapter with base model for evaluation.

This script creates a merged model that can be used with lm_eval
without vocabulary size mismatches.
"""

import sys
import argparse
import torch
import json
from pathlib import Path
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel


def load_vocabulary_info(lora_path):
    """
    Load vocabulary information from LoRA adapter directory.

    Args:
        lora_path: Path to LoRA adapter directory

    Returns:
        Dictionary with vocabulary information, or None if not found
    """
    vocab_info_path = Path(lora_path) / "vocabulary_info.json"

    if vocab_info_path.exists():
        try:
            with open(vocab_info_path, "r", encoding="utf-8") as f:
                vocab_info = json.load(f)
            print(f"📋 Loaded vocabulary info from {vocab_info_path}")
            return vocab_info
        except Exception as e:
            print(f"⚠️ Error loading vocabulary info: {e}")
            return None
    else:
        print(f"⚠️ No vocabulary info found at {vocab_info_path}")
        return None


def determine_target_vocabulary_size(lora_path, base_model_vocab_size):
    """
    Determine the target vocabulary size for merging.

    Args:
        lora_path: Path to LoRA adapter directory
        base_model_vocab_size: Base model vocabulary size

    Returns:
        Target vocabulary size for the merged model
    """
    # Try to load vocabulary info from LoRA adapter
    vocab_info = load_vocabulary_info(lora_path)

    if vocab_info and "final_vocab_size" in vocab_info:
        target_size = vocab_info["final_vocab_size"]
        print(f"📋 Using target vocabulary size from LoRA adapter: {target_size}")

        # Validate the information
        if "original_vocab_size" in vocab_info:
            original_size = vocab_info["original_vocab_size"]
            tokens_added = vocab_info.get("think_tokens_added", 0)
            expected_size = original_size + tokens_added

            if target_size == expected_size:
                print(f"✅ Vocabulary size calculation verified: {original_size} + {tokens_added} = {target_size}")
            else:
                print(f"⚠️ Vocabulary size mismatch in saved info: expected {expected_size}, got {target_size}")

        return target_size
    else:
        # Fallback: try to determine from tokenizer
        try:
            lora_tokenizer = AutoTokenizer.from_pretrained(lora_path, trust_remote_code=True)
            target_size = len(lora_tokenizer)
            print(f"📋 Using vocabulary size from LoRA tokenizer: {target_size}")
            return target_size
        except Exception as e:
            print(f"⚠️ Could not load LoRA tokenizer: {e}")
            # Last resort fallback - assume 2 think tokens were added
            target_size = base_model_vocab_size + 2
            print(f"⚠️ Using fallback: base size ({base_model_vocab_size}) + 2 think tokens = {target_size}")
            return target_size


def merge_lora_adapter(base_model_path, lora_path, output_path):
    """
    Merge LoRA adapter with base model and save as a complete model.

    Args:
        base_model_path: Path to base model
        lora_path: Path to LoRA adapter
        output_path: Path to save merged model
    """
    print(f"🔄 Merging LoRA adapter for evaluation")
    print(f"📋 Base model: {base_model_path}")
    print(f"📋 LoRA adapter: {lora_path}")
    print(f"📋 Output path: {output_path}")
    print()

    # Load base model first
    print("🔧 Loading base model...")
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_path,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True,
    )

    # Check base model vocabulary size
    original_vocab_size = base_model.get_input_embeddings().weight.shape[0]
    print(f"   Base model embedding size: {original_vocab_size}")

    # Determine target vocabulary size from LoRA adapter
    target_vocab_size = determine_target_vocabulary_size(lora_path, original_vocab_size)
    print(f"   Target vocab size for LoRA: {target_vocab_size}")

    # Load tokenizer from LoRA adapter (which has the correct vocabulary)
    print("📝 Loading tokenizer from LoRA adapter...")
    try:
        tokenizer = AutoTokenizer.from_pretrained(lora_path, trust_remote_code=True)
        print(f"   LoRA tokenizer vocab size: {len(tokenizer)}")

        # Verify tokenizer size matches target
        if len(tokenizer) != target_vocab_size:
            print(f"⚠️ Tokenizer size mismatch: {len(tokenizer)} vs expected {target_vocab_size}")
            print("   Using tokenizer size as authoritative")
            target_vocab_size = len(tokenizer)

    except Exception as e:
        print(f"⚠️ Could not load tokenizer from LoRA adapter: {e}")
        print("   Loading tokenizer from base model and adjusting...")
        tokenizer = AutoTokenizer.from_pretrained(base_model_path, trust_remote_code=True)
        print(f"   Base tokenizer vocab size: {len(tokenizer)}")

    # Resize base model if necessary
    if original_vocab_size != target_vocab_size:
        print(f"🔧 Resizing model embeddings: {original_vocab_size} → {target_vocab_size}")
        base_model.resize_token_embeddings(target_vocab_size)
    else:
        print("✅ Vocabulary sizes match - no resizing needed")

    # Load LoRA adapter
    print("🔗 Loading and merging LoRA adapter...")
    model = PeftModel.from_pretrained(base_model, lora_path)

    # Merge LoRA weights into base model
    print("🔀 Merging LoRA weights...")
    merged_model = model.merge_and_unload()

    # Save merged model
    output_path = Path(output_path)
    output_path.mkdir(parents=True, exist_ok=True)

    print(f"💾 Saving merged model to {output_path}...")
    merged_model.save_pretrained(output_path, safe_serialization=True)
    tokenizer.save_pretrained(output_path)

    print("✅ Merged model saved successfully!")
    print(f"📁 Model ready for evaluation at: {output_path}")

    return str(output_path)


def main():
    parser = argparse.ArgumentParser(description="Merge LoRA adapter for evaluation")
    parser.add_argument("lora_path", help="Path to LoRA adapter")
    parser.add_argument(
        "--base_model", default="Qwen/Qwen2.5-7B-Instruct", help="Base model path"
    )
    parser.add_argument(
        "--output_path",
        default="./eval_models/merged_model",
        help="Output path for merged model",
    )

    args = parser.parse_args()

    try:
        merged_path = merge_lora_adapter(
            args.base_model, args.lora_path, args.output_path
        )

        print(f"✅ Merged model saved to: {merged_path}")

        return 0

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())