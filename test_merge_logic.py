#!/usr/bin/env python3
"""
Test just the vocabulary detection logic from the merge script.
"""

import sys
import json
from pathlib import Path

# Add tools to path
sys.path.append('tools')

def test_vocabulary_detection():
    """Test the vocabulary detection logic."""
    print("🔍 Testing vocabulary detection logic...")
    
    try:
        from merge_lora import load_vocabulary_info, determine_target_vocabulary_size
        
        lora_path = "outputs/limo/original-817-lora"
        base_vocab_size = 152064  # What the merge script currently sees
        
        print(f"📂 Testing with LoRA path: {lora_path}")
        print(f"📊 Base model vocab size: {base_vocab_size}")
        
        # Test vocabulary info loading
        print("\n1️⃣ Loading vocabulary info...")
        vocab_info = load_vocabulary_info(lora_path)
        
        if vocab_info:
            print("✅ Vocabulary info loaded successfully:")
            for key, value in vocab_info.items():
                print(f"   {key}: {value}")
        else:
            print("❌ Failed to load vocabulary info")
            return False
            
        # Test target vocabulary size determination
        print("\n2️⃣ Determining target vocabulary size...")
        target_size = determine_target_vocabulary_size(lora_path, base_vocab_size)
        print(f"✅ Target vocabulary size determined: {target_size}")
        
        # Validate the result
        expected_size = vocab_info.get("final_vocab_size", 151667)
        if target_size == expected_size:
            print(f"✅ Target size matches expected: {target_size}")
        else:
            print(f"❌ Target size mismatch: got {target_size}, expected {expected_size}")
            return False
            
        # Check if resizing would be needed
        print(f"\n3️⃣ Checking if resizing would be needed...")
        resize_needed = base_vocab_size != target_size
        print(f"   Base size: {base_vocab_size}")
        print(f"   Target size: {target_size}")
        print(f"   Resize needed: {resize_needed}")
        
        if resize_needed:
            print(f"   Resize: {base_vocab_size} → {target_size}")
            print(f"   Difference: {target_size - base_vocab_size}")
        else:
            print("   ✅ No resizing needed!")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    print("🚀 Testing Merge Script Vocabulary Detection")
    print("=" * 50)
    
    success = test_vocabulary_detection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Vocabulary detection logic works correctly!")
        print("💡 The merge script should now properly detect vocabulary sizes.")
        print("\n📋 Expected behavior:")
        print("   - Load vocabulary info from LoRA adapter")
        print("   - Determine target size: 151667")
        print("   - Resize base model: 152064 → 151667")
        print("   - This is the correct behavior (base model needs resizing)")
    else:
        print("❌ Issues found with vocabulary detection logic.")
        
    print("\n💡 The key improvement:")
    print("   - Before: Hardcoded target size (151667)")
    print("   - After: Dynamic detection from LoRA adapter vocabulary info")
    print("   - Future: Training script will save correct vocabulary info")

if __name__ == "__main__":
    main()
