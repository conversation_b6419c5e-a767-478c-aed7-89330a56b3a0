#!/usr/bin/env python3
"""
Test vocabulary consistency between training and merging.
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.append('src')

from utils.model_utils import load_model_and_tokenizer
from tools.merge_lora import load_vocabulary_info, determine_target_vocabulary_size

def test_model_loading():
    """Test the updated model loading function."""
    print("🔍 Testing updated model loading function...")
    
    try:
        # Test loading base model with think tokens
        model, tokenizer = load_model_and_tokenizer(
            "Qwen/Qwen2.5-7B-Instruct",
            think_start="<think>",
            think_end="</think>",
            device_map="cpu",  # Use CPU to avoid GPU memory issues
        )
        
        print(f"✅ Model loaded successfully")
        print(f"   Tokenizer vocab size: {len(tokenizer)}")
        print(f"   Model embedding size: {model.get_input_embeddings().weight.shape[0]}")
        print(f"   Original vocab size: {getattr(model.config, 'original_vocab_size', 'Not set')}")
        print(f"   Final vocab size: {getattr(model.config, 'final_vocab_size', 'Not set')}")
        print(f"   Think tokens added: {getattr(model.config, 'think_tokens_added', 'Not set')}")
        
        # Check if sizes match
        if len(tokenizer) == model.get_input_embeddings().weight.shape[0]:
            print("✅ Tokenizer and model embedding sizes match")
        else:
            print("❌ Tokenizer and model embedding sizes don't match")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing model loading: {e}")
        return False

def test_vocabulary_info_loading():
    """Test loading vocabulary info from existing LoRA adapters."""
    print("\n🔍 Testing vocabulary info loading...")
    
    lora_paths = [
        "outputs/limo/original-817-lora",
        "outputs/limo/code_switched-817-lora"
    ]
    
    for lora_path in lora_paths:
        print(f"\n📂 Testing {lora_path}...")
        
        # Test vocabulary info loading
        vocab_info = load_vocabulary_info(lora_path)
        if vocab_info:
            print(f"✅ Vocabulary info loaded:")
            for key, value in vocab_info.items():
                print(f"   {key}: {value}")
        else:
            print("❌ No vocabulary info found")
            
        # Test target vocabulary size determination
        try:
            # Simulate base model vocab size (this would come from actual base model)
            base_vocab_size = 152064  # This is what we saw in the merge script
            target_size = determine_target_vocabulary_size(lora_path, base_vocab_size)
            print(f"✅ Target vocabulary size determined: {target_size}")
        except Exception as e:
            print(f"❌ Error determining target vocabulary size: {e}")

def test_merge_script_logic():
    """Test the merge script logic without actually merging."""
    print("\n🔍 Testing merge script logic...")
    
    # Test with existing LoRA adapter
    lora_path = "outputs/limo/original-817-lora"
    
    if not Path(lora_path).exists():
        print(f"❌ LoRA adapter not found: {lora_path}")
        return False
        
    try:
        # Simulate what the merge script does
        print(f"📂 Testing merge logic for {lora_path}")
        
        # Load vocabulary info
        vocab_info = load_vocabulary_info(lora_path)
        
        # Simulate base model vocab size
        base_vocab_size = 152064
        
        # Determine target size
        target_size = determine_target_vocabulary_size(lora_path, base_vocab_size)
        
        print(f"✅ Merge logic test completed:")
        print(f"   Base model vocab size: {base_vocab_size}")
        print(f"   Target vocab size: {target_size}")
        print(f"   Resize needed: {base_vocab_size != target_size}")
        
        if vocab_info:
            expected_size = vocab_info.get("final_vocab_size")
            if target_size == expected_size:
                print(f"✅ Target size matches expected size: {target_size}")
            else:
                print(f"❌ Target size mismatch: got {target_size}, expected {expected_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing merge logic: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Vocabulary Consistency")
    print("=" * 60)
    
    # Test 1: Model loading
    test1_passed = test_model_loading()
    
    # Test 2: Vocabulary info loading
    test_vocabulary_info_loading()
    
    # Test 3: Merge script logic
    test3_passed = test_merge_script_logic()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if test1_passed and test3_passed:
        print("🎉 All critical tests passed!")
        print("💡 The vocabulary consistency fixes should work correctly.")
    else:
        print("❌ Some tests failed. Check the output above.")
        
    print("\n💡 Next steps:")
    print("   1. Re-train a model to test the vocabulary info saving")
    print("   2. Test merging with the updated script")
    print("   3. Verify no vocabulary resizing is needed during merge")

if __name__ == "__main__":
    main()
