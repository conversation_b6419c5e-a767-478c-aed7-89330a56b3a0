#!/usr/bin/env python3
import json
from pathlib import Path

# Test vocabulary info loading directly
lora_path = "outputs/limo/original-817-lora"
vocab_info_path = Path(lora_path) / "vocabulary_info.json"

print(f"Testing vocabulary info loading from: {vocab_info_path}")

if vocab_info_path.exists():
    with open(vocab_info_path, "r") as f:
        vocab_info = json.load(f)
    print("✅ Vocabulary info loaded:")
    for key, value in vocab_info.items():
        print(f"   {key}: {value}")
else:
    print("❌ Vocabulary info file not found")

print("\n✅ Basic test completed successfully!")
