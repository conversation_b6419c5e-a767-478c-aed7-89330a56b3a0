#!/usr/bin/env python3
"""
Test vocabulary info functionality without loading full models.
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.append('src')
sys.path.append('tools')

def test_vocabulary_info_loading():
    """Test loading vocabulary info from existing LoRA adapters."""
    print("🔍 Testing vocabulary info loading...")
    
    # Import the functions
    try:
        from merge_lora import load_vocabulary_info, determine_target_vocabulary_size
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    lora_paths = [
        "outputs/limo/original-817-lora",
        "outputs/limo/code_switched-817-lora"
    ]
    
    all_passed = True
    
    for lora_path in lora_paths:
        print(f"\n📂 Testing {lora_path}...")
        
        if not Path(lora_path).exists():
            print(f"❌ Path does not exist: {lora_path}")
            all_passed = False
            continue
            
        # Test vocabulary info loading
        vocab_info = load_vocabulary_info(lora_path)
        if vocab_info:
            print(f"✅ Vocabulary info loaded:")
            for key, value in vocab_info.items():
                print(f"   {key}: {value}")
        else:
            print("⚠️ No vocabulary info found (expected for existing models)")
            
        # Test target vocabulary size determination
        try:
            # Simulate base model vocab size (this is what we saw in the merge script)
            base_vocab_size = 152064
            target_size = determine_target_vocabulary_size(lora_path, base_vocab_size)
            print(f"✅ Target vocabulary size determined: {target_size}")
            
            # The target should be around 151667 (base 151665 + 2 think tokens)
            if 151665 <= target_size <= 151670:
                print(f"✅ Target size is in expected range")
            else:
                print(f"⚠️ Target size {target_size} is outside expected range (151665-151670)")
                
        except Exception as e:
            print(f"❌ Error determining target vocabulary size: {e}")
            all_passed = False
    
    return all_passed

def test_tokenizer_loading():
    """Test loading tokenizers from LoRA adapters."""
    print("\n🔍 Testing tokenizer loading from LoRA adapters...")
    
    from transformers import AutoTokenizer
    
    lora_paths = [
        "outputs/limo/original-817-lora",
        "outputs/limo/code_switched-817-lora"
    ]
    
    for lora_path in lora_paths:
        print(f"\n📂 Testing tokenizer from {lora_path}...")
        
        try:
            tokenizer = AutoTokenizer.from_pretrained(lora_path, trust_remote_code=True)
            vocab_size = len(tokenizer)
            print(f"✅ Tokenizer loaded successfully")
            print(f"   Vocabulary size: {vocab_size}")
            
            # Check for think tokens
            vocab = tokenizer.get_vocab()
            think_start_id = vocab.get("<think>", None)
            think_end_id = vocab.get("</think>", None)
            
            if think_start_id is not None and think_end_id is not None:
                print(f"✅ Think tokens found: <think>={think_start_id}, </think>={think_end_id}")
                expected_vocab_size = max(think_start_id, think_end_id) + 1
                if vocab_size == expected_vocab_size:
                    print(f"✅ Vocabulary size matches token positions: {vocab_size}")
                else:
                    print(f"⚠️ Vocabulary size mismatch: {vocab_size} vs expected {expected_vocab_size}")
            else:
                print(f"❌ Think tokens not found in vocabulary")
                
        except Exception as e:
            print(f"❌ Error loading tokenizer: {e}")

def create_test_vocabulary_info():
    """Create test vocabulary info files for existing LoRA adapters."""
    print("\n🔧 Creating test vocabulary info files...")
    
    lora_paths = [
        "outputs/limo/original-817-lora",
        "outputs/limo/code_switched-817-lora"
    ]
    
    for lora_path in lora_paths:
        if not Path(lora_path).exists():
            continue
            
        print(f"📂 Creating vocabulary info for {lora_path}...")
        
        try:
            from transformers import AutoTokenizer
            tokenizer = AutoTokenizer.from_pretrained(lora_path, trust_remote_code=True)
            
            # Create vocabulary info based on tokenizer
            vocab_info = {
                "original_vocab_size": 151665,  # Base Qwen2.5-7B-Instruct size
                "final_vocab_size": len(tokenizer),
                "think_tokens_added": 2,
                "tokenizer_vocab_size": len(tokenizer),
                "model_embedding_size": len(tokenizer),  # Should match after training
            }
            
            # Save vocabulary info
            vocab_info_path = Path(lora_path) / "vocabulary_info.json"
            with open(vocab_info_path, "w", encoding="utf-8") as f:
                json.dump(vocab_info, f, indent=2)
                
            print(f"✅ Created vocabulary info at {vocab_info_path}")
            print(f"   Content: {vocab_info}")
            
        except Exception as e:
            print(f"❌ Error creating vocabulary info: {e}")

def main():
    """Run all tests."""
    print("🚀 Testing Vocabulary Info Functionality")
    print("=" * 60)
    
    # Test 1: Vocabulary info loading (will show no info for existing models)
    test1_passed = test_vocabulary_info_loading()
    
    # Test 2: Tokenizer loading
    test_tokenizer_loading()
    
    # Test 3: Create test vocabulary info files
    create_test_vocabulary_info()
    
    # Test 4: Test vocabulary info loading again (should work now)
    print("\n" + "=" * 40)
    print("🔄 Re-testing with created vocabulary info...")
    test1_passed_retry = test_vocabulary_info_loading()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if test1_passed_retry:
        print("🎉 Vocabulary info functionality works correctly!")
        print("💡 The merge script should now be able to determine vocabulary sizes automatically.")
    else:
        print("❌ Some issues remain. Check the output above.")
        
    print("\n💡 Next steps:")
    print("   1. Test the updated merge script with existing LoRA adapters")
    print("   2. Re-train a model to test the automatic vocabulary info saving")
    print("   3. Verify no vocabulary resizing warnings appear during merge")

if __name__ == "__main__":
    main()
